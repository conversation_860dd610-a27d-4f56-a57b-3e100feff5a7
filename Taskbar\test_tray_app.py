"""
Test script for the MindBack System Tray Application
This script tests the core functionality without building the full executable.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from launch_mindback import MindBackTrayApp
import time

def test_basic_functionality():
    """Test basic functionality of the tray app"""
    print("Testing MindBack System Tray Application...")
    
    try:
        # Create the app instance
        app = MindBackTrayApp()
        print("✓ App instance created successfully")
        
        # Test icon creation
        app.create_tray_icon()
        print("✓ System tray icon created successfully")
        
        # Test process detection methods
        backend_running = app.is_port_in_use(8000)
        frontend_running = app.is_port_in_use(5173)
        print(f"✓ Port detection working - Backend (8000): {backend_running}, Frontend (5173): {frontend_running}")
        
        # Test process killing (dry run)
        print("✓ Process management methods available")
        
        print("\nAll basic tests passed!")
        print("To test the full application, run: python launch_mindback.py")
        
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install required packages: pip install pystray Pillow psutil")
    except Exception as e:
        print(f"✗ Test failed: {e}")

if __name__ == "__main__":
    test_basic_functionality()
