
## Phase 1: Minimal Change - Copy Existing Pattern
- [x] **1.1** Find the existing teleological block in `backend/api/routes/llm.py` (around line 128)
- [x] **1.2** Copy the exact same logic and add it as an `elif` block for exploratory
- [x] **1.3** Change only these 2 things:
  - [x] `'teleological'` → `'exploratory'` 
  - [x] `'initiate_mindmap2'` → `'initiate_chatfork2'`
- [ ] **1.4** Test that teleological still works (regression test)
- [ ] **1.5** Test that exploratory now works


- [ ] we can remove the header of the chat fork with the text fork chat and the buttons reset and debug and then there is a not functioning (X) button which we do not need as we have  a (x) for each forked chat

## Settings (2025.06.08)
  - presently we have context, expant with
    - techstack, listing all apps being wrapped and their API
      - Firecrawl
      - Exe
      - 
    - knowledge Source
      - EU
      - Destatis
      - Polymarket
      - 
    - setting hooks at sources with changing data
