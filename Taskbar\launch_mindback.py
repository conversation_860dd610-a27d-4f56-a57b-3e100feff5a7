"""
MindBack System Tray Application - Simple Start/Stop for MindBack servers
"""

import subprocess
import os
import sys
import time
import threading
import psutil
from pathlib import Path
import tkinter as tk
from tkinter import messagebox
import pystray
from pystray import MenuItem as item
from PIL import Image, ImageDraw

class MindBackTrayApp:
    def __init__(self):
        self.project_root = Path(r"C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1")
        self.ps_script = self.project_root / "run_setup.ps1"
        self.icon_path = self.project_root / "Taskbar" / "MB_logo.ico"

        # Simple state tracking
        self.is_running = False
        self.setup_process = None

        # Create system tray icon
        self.create_tray_icon()



    def create_tray_icon(self):
        """Create the system tray icon with context menu"""
        # Try to load the custom icon, fallback to generated icon
        try:
            if self.icon_path.exists():
                image = Image.open(str(self.icon_path))
            else:
                # Generate a simple icon if MB_logo.ico doesn't exist
                image = self.create_default_icon()
        except Exception:
            image = self.create_default_icon()

        # Create the menu
        menu = pystray.Menu(
            item('MindBack Status', self.show_status, enabled=False),
            pystray.Menu.SEPARATOR,
            item('Start MindBack', self.start_mindback, enabled=lambda item: not self.is_running),
            item('Stop MindBack', self.stop_mindback, enabled=lambda item: self.is_running),
            pystray.Menu.SEPARATOR,
            item('Open Frontend', self.open_frontend, enabled=lambda item: self.is_running),
            item('Open Backend Docs', self.open_backend_docs, enabled=lambda item: self.is_running),
            pystray.Menu.SEPARATOR,
            item('Exit', self.quit_application)
        )

        # Create the icon
        self.icon = pystray.Icon("MindBack", image, "MindBack Controller", menu)

    def create_default_icon(self):
        """Create a default icon if the custom icon is not available"""
        # Create a simple 64x64 icon with "MB" text
        width = height = 64
        image = Image.new('RGB', (width, height), color='black')
        draw = ImageDraw.Draw(image)

        # Draw "MB" text
        try:
            # Try to use a larger font
            from PIL import ImageFont
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()

        text = "MB"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        x = (width - text_width) // 2
        y = (height - text_height) // 2

        draw.text((x, y), text, fill='white', font=font)

        return image

    def show_status(self, icon, item):
        """Show current status (disabled menu item for display)"""
        pass

    def start_mindback(self, icon, item):
        """Start MindBack servers"""
        if self.is_running:
            self.show_message("MindBack is already running!")
            return

        try:
            # Stop any existing processes first
            self.stop_all_processes()

            self.is_running = True
            self.update_icon_tooltip("MindBack - Starting...")

            # Run the setup script
            self.setup_process = subprocess.Popen(
                ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(self.ps_script)],
                cwd=str(self.project_root),
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            # Start monitoring in background
            threading.Thread(target=self.monitor_startup, daemon=True).start()

            self.show_message("MindBack is starting...\nCheck http://localhost:5173 in a few moments.")

        except Exception as e:
            self.is_running = False
            self.show_message(f"Failed to start MindBack: {str(e)}")
            self.update_icon_tooltip("MindBack - Stopped")

    def stop_mindback(self, icon, item):
        """Stop MindBack servers"""
        try:
            self.update_icon_tooltip("MindBack - Stopping...")
            self.stop_all_processes()
            self.is_running = False
            self.setup_process = None
            self.update_icon_tooltip("MindBack - Stopped")
            self.show_message("MindBack servers stopped.")
        except Exception as e:
            self.show_message(f"Error stopping MindBack: {str(e)}")

    def stop_all_processes(self):
        """Stop all MindBack-related processes"""
        # Simple approach - kill by port and process name
        try:
            # Kill processes using our ports
            subprocess.run(['taskkill', '/f', '/fi', 'PID eq 8000'], capture_output=True)
            subprocess.run(['taskkill', '/f', '/fi', 'PID eq 5173'], capture_output=True)

            # Kill by process name patterns
            subprocess.run(['taskkill', '/f', '/im', 'uvicorn.exe'], capture_output=True)
            subprocess.run(['taskkill', '/f', '/im', 'node.exe'], capture_output=True)

            # Kill PowerShell running our script
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'powershell' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                        if 'run_setup.ps1' in cmdline:
                            proc.terminate()
                except:
                    pass

        except Exception:
            pass  # Ignore errors, just try to clean up

    def monitor_startup(self):
        """Simple monitoring - just check if ports become available"""
        for i in range(24):  # Check for 2 minutes (24 * 5 seconds)
            if not self.is_running:
                break

            time.sleep(5)

            if self.is_port_in_use(5173):
                self.update_icon_tooltip("MindBack - Running")
                # Auto-open browser once
                if i > 6:  # Wait at least 30 seconds
                    try:
                        subprocess.run(['start', 'http://localhost:5173'], shell=True)
                    except:
                        pass
                break
        else:
            # Timeout
            if self.is_running:
                self.update_icon_tooltip("MindBack - Check Status")
                self.show_message("Startup taking longer than expected.\nCheck http://localhost:5173 manually.")

    def is_port_in_use(self, port):
        """Check if a port is in use"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except Exception:
            return False

    def open_frontend(self, icon, item):
        """Open the frontend in the default browser"""
        try:
            subprocess.run(['start', 'http://localhost:5173'], shell=True, check=True)
        except Exception as e:
            self.show_message(f"Failed to open frontend: {str(e)}")

    def open_backend_docs(self, icon, item):
        """Open the backend API documentation in the default browser"""
        try:
            subprocess.run(['start', 'http://localhost:8000/docs'], shell=True, check=True)
        except Exception as e:
            self.show_message(f"Failed to open backend docs: {str(e)}")

    def update_icon_tooltip(self, tooltip):
        """Update the icon tooltip"""
        if hasattr(self, 'icon'):
            self.icon.title = tooltip

    def show_message(self, message):
        """Show a message box"""
        # Create a temporary root window for the message box
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        messagebox.showinfo("MindBack", message)
        root.destroy()

    def quit_application(self, icon, item):
        """Quit the application"""
        if self.is_running:
            self.stop_all_processes()
        self.icon.stop()

    def run(self):
        """Run the system tray application"""
        self.icon.run()

if __name__ == "__main__":
    app = MindBackTrayApp()
    app.run()
