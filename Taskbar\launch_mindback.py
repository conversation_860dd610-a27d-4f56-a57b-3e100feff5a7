"""
MindBack System Tray Application
Provides start/stop functionality for MindBack servers through a system tray icon.
"""

import subprocess
import os
import sys
import time
import threading
import psutil
from pathlib import Path
import tkinter as tk
from tkinter import messagebox
import pystray
from pystray import MenuItem as item
from PIL import Image, ImageDraw
import tempfile
import atexit

class MindBackTrayApp:
    def __init__(self):
        # Set up lock file for single instance
        self.lock_file_path = Path(tempfile.gettempdir()) / "mindback_tray.lock"

        # Check if another instance is already running
        self.check_single_instance()

        self.project_root = Path(r"C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1")
        self.ps_script = self.project_root / "run_setup.ps1"
        self.icon_path = self.project_root / "Taskbar" / "MB_logo.ico"

        # Process tracking
        self.backend_process = None
        self.frontend_process = None
        self.setup_process = None
        self.is_running = False

        # Create lock file
        self.create_lock_file()

        # Create system tray icon
        self.create_tray_icon()

    def check_single_instance(self):
        """Ensure only one instance of the tray app is running using lock file"""
        if self.lock_file_path.exists():
            try:
                # Read the PID from the lock file
                with open(self.lock_file_path, 'r') as f:
                    stored_pid = int(f.read().strip())

                # Check if the process with that PID is still running
                try:
                    proc = psutil.Process(stored_pid)
                    if proc.is_running():
                        # Check if it's actually our process
                        cmdline = ' '.join(proc.cmdline()).lower()
                        if 'launch_mindback' in cmdline:
                            self.show_message("MindBack System Tray is already running!\nCheck your system tray area.")
                            sys.exit(0)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    # Process doesn't exist anymore, remove stale lock file
                    self.lock_file_path.unlink(missing_ok=True)

            except (ValueError, FileNotFoundError):
                # Invalid lock file, remove it
                self.lock_file_path.unlink(missing_ok=True)

    def create_lock_file(self):
        """Create a lock file with current PID"""
        try:
            with open(self.lock_file_path, 'w') as f:
                f.write(str(os.getpid()))

            # Register cleanup function
            atexit.register(self.cleanup_lock_file)
        except Exception as e:
            print(f"Warning: Could not create lock file: {e}")

    def cleanup_lock_file(self):
        """Remove the lock file on exit"""
        try:
            if self.lock_file_path.exists():
                self.lock_file_path.unlink()
        except Exception:
            pass  # Ignore errors during cleanup

    def create_tray_icon(self):
        """Create the system tray icon with context menu"""
        # Try to load the custom icon, fallback to generated icon
        try:
            if self.icon_path.exists():
                image = Image.open(str(self.icon_path))
            else:
                # Generate a simple icon if MB_logo.ico doesn't exist
                image = self.create_default_icon()
        except Exception:
            image = self.create_default_icon()

        # Create the menu
        menu = pystray.Menu(
            item('MindBack Status', self.show_status, enabled=False),
            pystray.Menu.SEPARATOR,
            item('Start MindBack', self.start_mindback, enabled=lambda item: not self.is_running),
            item('Stop MindBack', self.stop_mindback, enabled=lambda item: self.is_running),
            pystray.Menu.SEPARATOR,
            item('Open Frontend', self.open_frontend, enabled=lambda item: self.is_running),
            item('Open Backend Docs', self.open_backend_docs, enabled=lambda item: self.is_running),
            pystray.Menu.SEPARATOR,
            item('Exit', self.quit_application)
        )

        # Create the icon
        self.icon = pystray.Icon("MindBack", image, "MindBack Controller", menu)

    def create_default_icon(self):
        """Create a default icon if the custom icon is not available"""
        # Create a simple 64x64 icon with "MB" text
        width = height = 64
        image = Image.new('RGB', (width, height), color='black')
        draw = ImageDraw.Draw(image)

        # Draw "MB" text
        try:
            # Try to use a larger font
            from PIL import ImageFont
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()

        text = "MB"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        x = (width - text_width) // 2
        y = (height - text_height) // 2

        draw.text((x, y), text, fill='white', font=font)

        return image

    def show_status(self, icon, item):
        """Show current status (disabled menu item for display)"""
        pass

    def start_mindback(self, icon, item):
        """Start MindBack servers"""
        if self.is_running:
            self.show_message("MindBack is already running!")
            return

        try:
            # First, ensure any existing processes are stopped
            self.kill_existing_processes()
            time.sleep(2)  # Give processes time to stop

            # Start the setup script in a separate thread
            self.is_running = True
            self.update_icon_tooltip("MindBack - Starting...")

            # Check if the PowerShell script exists
            if not self.ps_script.exists():
                raise FileNotFoundError(f"Setup script not found: {self.ps_script}")

            # Run the PowerShell script with better error handling
            self.setup_process = subprocess.Popen(
                ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(self.ps_script)],
                cwd=str(self.project_root),
                creationflags=subprocess.CREATE_NO_WINDOW,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Start monitoring thread
            threading.Thread(target=self.monitor_processes, daemon=True).start()

            # Start a thread to open browser after servers are ready
            threading.Thread(target=self.wait_and_open_browser, daemon=True).start()

            self.show_message("MindBack is starting...\nPlease wait 30-60 seconds for servers to be ready.\nBrowser will open automatically when ready.")

        except Exception as e:
            self.is_running = False
            self.show_message(f"Failed to start MindBack: {str(e)}")
            self.update_icon_tooltip("MindBack - Stopped")

    def stop_mindback(self, icon, item):
        """Stop MindBack servers"""
        if not self.is_running:
            self.show_message("MindBack is not running!")
            return

        try:
            self.update_icon_tooltip("MindBack - Stopping...")

            # Kill all related processes
            self.kill_existing_processes()

            self.is_running = False
            self.setup_process = None
            self.backend_process = None
            self.frontend_process = None

            self.update_icon_tooltip("MindBack - Stopped")
            self.show_message("MindBack servers have been stopped.")

        except Exception as e:
            self.show_message(f"Error stopping MindBack: {str(e)}")

    def kill_existing_processes(self):
        """Kill existing MindBack-related processes"""
        processes_to_kill = []

        print("Searching for existing MindBack processes...")

        # Find processes by name and command line
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                name = proc_info['name'].lower()
                cmdline = ' '.join(proc_info['cmdline'] or []).lower()

                # Kill uvicorn processes (backend)
                if 'uvicorn' in name or 'uvicorn' in cmdline:
                    processes_to_kill.append((proc, f"uvicorn backend (PID: {proc.pid})"))

                # Kill node processes running our frontend
                elif 'node' in name and ('vite' in cmdline or '5173' in cmdline or 'mindback' in cmdline):
                    processes_to_kill.append((proc, f"node frontend (PID: {proc.pid})"))

                # Kill PowerShell processes running our setup script
                elif 'powershell' in name and 'run_setup.ps1' in cmdline:
                    processes_to_kill.append((proc, f"PowerShell setup (PID: {proc.pid})"))

                # Kill Python processes that might be running our backend
                elif 'python' in name and ('main:app' in cmdline or 'mindback' in cmdline or 'uvicorn' in cmdline):
                    processes_to_kill.append((proc, f"Python backend (PID: {proc.pid})"))

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        # Kill the processes
        killed_count = 0
        for proc, description in processes_to_kill:
            try:
                print(f"Stopping {description}")
                proc.terminate()
                # Wait a bit for graceful termination
                try:
                    proc.wait(timeout=3)
                    killed_count += 1
                except psutil.TimeoutExpired:
                    proc.kill()  # Force kill if graceful termination fails
                    killed_count += 1
            except psutil.NoSuchProcess:
                pass  # Process already gone
            except Exception as e:
                print(f"Error stopping {description}: {e}")

        if killed_count > 0:
            print(f"Stopped {killed_count} existing processes")
        else:
            print("No existing MindBack processes found")

    def wait_and_open_browser(self):
        """Wait for servers to be ready and then open the browser"""
        max_wait_time = 120  # 2 minutes max wait time
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            if self.is_port_in_use(8000) and self.is_port_in_use(5173):
                # Both servers are running, wait a bit more for full initialization
                time.sleep(5)
                try:
                    # Open the frontend in the browser
                    subprocess.run(['start', 'http://localhost:5173'], shell=True, check=True)
                    break
                except Exception as e:
                    print(f"Failed to open browser: {e}")
                    break
            time.sleep(3)  # Check every 3 seconds

    def monitor_processes(self):
        """Monitor the health of MindBack processes"""
        startup_time = time.time()
        max_startup_time = 120  # 2 minutes max startup time

        while self.is_running:
            try:
                # Check if setup process is still running
                if self.setup_process and self.setup_process.poll() is not None:
                    # Setup process has ended, check for errors
                    stdout, stderr = self.setup_process.communicate()
                    if stderr and "error" in stderr.lower():
                        self.is_running = False
                        self.update_icon_tooltip("MindBack - Startup Failed")
                        self.show_message(f"MindBack startup failed with error:\n{stderr[:200]}...")
                        break

                    # Check if servers are actually running after some time
                    if time.time() - startup_time > 30:  # Give it at least 30 seconds
                        backend_running = self.is_port_in_use(8000)
                        frontend_running = self.is_port_in_use(5173)

                        if backend_running and frontend_running:
                            self.update_icon_tooltip("MindBack - Running")
                        elif time.time() - startup_time > max_startup_time:
                            # Startup failed
                            self.is_running = False
                            self.update_icon_tooltip("MindBack - Startup Failed")
                            self.show_message("MindBack startup failed. Servers did not start within the expected time.")
                            break

                # Update tooltip based on current status
                if self.is_running:
                    if time.time() - startup_time < 30:
                        self.update_icon_tooltip("MindBack - Starting...")
                    elif self.is_port_in_use(8000) and self.is_port_in_use(5173):
                        self.update_icon_tooltip("MindBack - Running")
                    else:
                        self.update_icon_tooltip("MindBack - Starting...")

                time.sleep(5)  # Check every 5 seconds

            except Exception as e:
                print(f"Monitor error: {e}")
                time.sleep(5)
                continue

    def is_port_in_use(self, port):
        """Check if a port is in use"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except Exception:
            return False

    def open_frontend(self, icon, item):
        """Open the frontend in the default browser"""
        try:
            subprocess.run(['start', 'http://localhost:5173'], shell=True, check=True)
        except Exception as e:
            self.show_message(f"Failed to open frontend: {str(e)}")

    def open_backend_docs(self, icon, item):
        """Open the backend API documentation in the default browser"""
        try:
            subprocess.run(['start', 'http://localhost:8000/docs'], shell=True, check=True)
        except Exception as e:
            self.show_message(f"Failed to open backend docs: {str(e)}")

    def update_icon_tooltip(self, tooltip):
        """Update the icon tooltip"""
        if hasattr(self, 'icon'):
            self.icon.title = tooltip

    def show_message(self, message):
        """Show a message box"""
        # Create a temporary root window for the message box
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        messagebox.showinfo("MindBack", message)
        root.destroy()

    def quit_application(self, icon, item):
        """Quit the application"""
        if self.is_running:
            # Ask for confirmation if servers are running
            root = tk.Tk()
            root.withdraw()
            result = messagebox.askyesno("MindBack", "MindBack servers are running. Stop them and exit?")
            root.destroy()

            if result:
                self.stop_mindback(None, None)
            else:
                return

        # Clean up lock file
        self.cleanup_lock_file()
        self.icon.stop()

    def run(self):
        """Run the system tray application"""
        self.icon.run()

if __name__ == "__main__":
    app = MindBackTrayApp()
    app.run()
