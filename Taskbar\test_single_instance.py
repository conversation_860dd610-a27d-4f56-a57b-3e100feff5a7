#!/usr/bin/env python3
"""
Test script to verify the single instance checking works correctly
"""

import sys
import os
import time
import tempfile
from pathlib import Path
import psutil

def test_lock_file_approach():
    """Test the lock file approach for single instance checking"""
    lock_file_path = Path(tempfile.gettempdir()) / "mindback_tray.lock"
    
    print(f"Lock file path: {lock_file_path}")
    
    # Clean up any existing lock file
    if lock_file_path.exists():
        print("Removing existing lock file...")
        lock_file_path.unlink()
    
    # Test 1: No existing instance
    print("\nTest 1: No existing instance")
    if lock_file_path.exists():
        print("❌ Lock file should not exist")
        return False
    else:
        print("✅ No lock file exists - good")
    
    # Test 2: Create lock file
    print("\nTest 2: Creating lock file")
    try:
        with open(lock_file_path, 'w') as f:
            f.write(str(os.getpid()))
        print(f"✅ Lock file created with PID: {os.getpid()}")
    except Exception as e:
        print(f"❌ Failed to create lock file: {e}")
        return False
    
    # Test 3: Check if lock file contains correct PID
    print("\nTest 3: Verifying lock file content")
    try:
        with open(lock_file_path, 'r') as f:
            stored_pid = int(f.read().strip())
        
        if stored_pid == os.getpid():
            print(f"✅ Lock file contains correct PID: {stored_pid}")
        else:
            print(f"❌ Lock file contains wrong PID: {stored_pid} (expected: {os.getpid()})")
            return False
    except Exception as e:
        print(f"❌ Failed to read lock file: {e}")
        return False
    
    # Test 4: Simulate checking for existing instance
    print("\nTest 4: Simulating instance check")
    try:
        proc = psutil.Process(stored_pid)
        if proc.is_running():
            print(f"✅ Process {stored_pid} is running")
            cmdline = ' '.join(proc.cmdline()).lower()
            print(f"Command line: {cmdline}")
        else:
            print(f"❌ Process {stored_pid} is not running")
            return False
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        print(f"❌ Could not check process: {e}")
        return False
    
    # Test 5: Clean up
    print("\nTest 5: Cleaning up")
    try:
        lock_file_path.unlink()
        print("✅ Lock file removed successfully")
    except Exception as e:
        print(f"❌ Failed to remove lock file: {e}")
        return False
    
    return True

def test_stale_lock_file():
    """Test handling of stale lock files"""
    lock_file_path = Path(tempfile.gettempdir()) / "mindback_tray.lock"
    
    print(f"\nTesting stale lock file handling...")
    
    # Create a lock file with a non-existent PID
    fake_pid = 99999  # Very unlikely to exist
    try:
        with open(lock_file_path, 'w') as f:
            f.write(str(fake_pid))
        print(f"Created lock file with fake PID: {fake_pid}")
    except Exception as e:
        print(f"❌ Failed to create fake lock file: {e}")
        return False
    
    # Try to check if the process exists
    try:
        proc = psutil.Process(fake_pid)
        if proc.is_running():
            print(f"❌ Fake process {fake_pid} should not be running")
            return False
    except psutil.NoSuchProcess:
        print(f"✅ Fake process {fake_pid} correctly detected as non-existent")
        # Remove stale lock file
        lock_file_path.unlink(missing_ok=True)
        print("✅ Stale lock file removed")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("Testing MindBack Single Instance Checking")
    print("=" * 50)
    
    success = True
    
    # Test the lock file approach
    if test_lock_file_approach():
        print("\n✅ Lock file approach test PASSED")
    else:
        print("\n❌ Lock file approach test FAILED")
        success = False
    
    # Test stale lock file handling
    if test_stale_lock_file():
        print("\n✅ Stale lock file test PASSED")
    else:
        print("\n❌ Stale lock file test FAILED")
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests PASSED! The single instance checking should work correctly.")
    else:
        print("💥 Some tests FAILED! There may be issues with the single instance checking.")
    
    print("\nThe improved system should now:")
    print("- Use a lock file instead of process scanning")
    print("- Handle stale lock files properly")
    print("- Be more reliable and less prone to false positives")
