# MindBack System Tray Controller

This folder contains a comprehensive Windows system tray application for managing MindBack servers.

## Features:

### System Tray Application (`launch_mindback.exe`)
- **System tray icon** with right-click context menu
- **Start MindBack** - Launches both backend and frontend servers
- **Stop MindBack** - Cleanly stops all MindBack processes
- **Status indication** - Shows current server status (Starting/Running/Stopped)
- **Quick access** - Direct links to Frontend and Backend documentation
- **Process monitoring** - Automatically detects server health
- **Graceful shutdown** - Properly terminates all related processes

### Additional Scripts
1. **`stop_mindback.ps1`** - PowerShell script for stopping servers
2. **`stop_mindback.bat`** - Batch wrapper for the stop script
3. **`setup_tray_app.ps1`** - Setup script to build and configure the application

## Quick Setup Instructions:

### Automatic Setup (Recommended):
```powershell
# Run this in PowerShell as Administrator
cd "C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\Taskbar"
.\setup_tray_app.ps1
```

This will:
1. Install required Python dependencies
2. Build the executable
3. Create desktop shortcuts
4. Provide instructions for pinning to taskbar

### Manual Setup:
1. Install dependencies: `pip install pystray Pillow psutil pyinstaller`
2. Build executable: `pyinstaller launch_mindback.spec`
3. Create shortcut to `dist\launch_mindback.exe`
4. Pin shortcut to taskbar

## How to Use:

### System Tray Icon:
1. **Single-click** the taskbar icon to see it in the system tray
2. **Right-click** the system tray icon for the context menu:
   - **Start MindBack** - Launches servers (disabled when running)
   - **Stop MindBack** - Stops servers (disabled when stopped)
   - **Open Frontend** - Opens http://localhost:5173 (when running)
   - **Open Backend Docs** - Opens http://localhost:8000/docs (when running)
   - **Exit** - Closes the tray application

### Status Indicators:
- **MindBack - Stopped** - No servers running
- **MindBack - Starting...** - Servers are starting up
- **MindBack - Running** - Both servers are active
- **MindBack - Startup Failed** - Something went wrong during startup

## Files in this Directory:

### Core Application:
- **`launch_mindback.py`** - Main system tray application source code
- **`launch_mindback.spec`** - PyInstaller configuration
- **`dist/launch_mindback.exe`** - Compiled executable
- **`requirements.txt`** - Python dependencies

### Utility Scripts:
- **`stop_mindback.ps1`** - PowerShell script to stop all MindBack processes
- **`stop_mindback.bat`** - Batch wrapper for the stop script
- **`setup_tray_app.ps1`** - Setup and build script

### Legacy Files:
- **`start_mindback.bat`** - Original batch launcher
- **`start_mindback_silent.vbs`** - VBScript wrapper

### Assets:
- **`MB_logo.ico`** - MindBack icon

## Troubleshooting:

### If servers won't start:
1. Right-click tray icon → Stop MindBack
2. Wait 5 seconds
3. Right-click tray icon → Start MindBack

### If the tray icon is unresponsive:
1. Open Task Manager
2. End the `launch_mindback.exe` process
3. Restart from taskbar

### If processes are stuck:
1. Use the "Stop MindBack.lnk" desktop shortcut
2. Or run `stop_mindback.bat` directly

## Advanced Features:

### Process Management:
- Automatically detects and kills existing MindBack processes before starting
- Monitors server health and updates status accordingly
- Handles both graceful termination and force-kill scenarios

### Port Detection:
- Checks if ports 8000 (backend) and 5173 (frontend) are in use
- Updates status based on actual port availability
- Prevents conflicts with other applications

### Error Handling:
- Shows informative error messages for common issues
- Provides fallback options when operations fail
- Logs detailed information for troubleshooting