"""
Simple MindBack System Tray - Start/Stop Only
"""
import subprocess
import os
import sys
import time
import threading
import psutil
from pathlib import Path
import pystray
from pystray import MenuItem as item
from PIL import Image, ImageDraw

class SimpleMindBackTray:
    def __init__(self):
        self.project_root = Path(r"C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1")
        self.ps_script = self.project_root / "run_setup.ps1"
        self.icon_path = self.project_root / "Taskbar" / "MB_logo.ico"
        
        self.is_running = False
        self.setup_process = None
        
        self.create_icon()
    
    def create_icon(self):
        """Create system tray icon"""
        # Load or create icon
        try:
            if self.icon_path.exists():
                image = Image.open(str(self.icon_path))
            else:
                image = self.create_default_icon()
        except:
            image = self.create_default_icon()
        
        # Create menu
        menu = pystray.Menu(
            item('Start MindBack', self.start_servers, enabled=lambda item: not self.is_running),
            item('Stop MindBack', self.stop_servers, enabled=lambda item: self.is_running),
            pystray.Menu.SEPARATOR,
            item('Exit', self.quit_app)
        )
        
        self.icon = pystray.Icon("MindBack", image, "MindBack Controller", menu)
    
    def create_default_icon(self):
        """Create simple MB icon"""
        image = Image.new('RGB', (64, 64), color='black')
        draw = ImageDraw.Draw(image)
        draw.text((20, 20), "MB", fill='white')
        return image
    
    def start_servers(self, icon, item):
        """Start MindBack servers"""
        if self.is_running:
            return
            
        try:
            # Kill any existing processes first
            self.kill_processes()
            time.sleep(2)
            
            # Start the setup script
            self.setup_process = subprocess.Popen(
                ["powershell", "-ExecutionPolicy", "Bypass", "-File", str(self.ps_script)],
                cwd=str(self.project_root),
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            self.is_running = True
            self.icon.title = "MindBack - Starting..."
            
            # Monitor in background
            threading.Thread(target=self.monitor, daemon=True).start()
            
        except Exception as e:
            self.is_running = False
            self.icon.title = f"MindBack - Error: {str(e)}"
    
    def stop_servers(self, icon, item):
        """Stop MindBack servers"""
        if not self.is_running:
            return
            
        self.kill_processes()
        self.is_running = False
        self.setup_process = None
        self.icon.title = "MindBack - Stopped"
    
    def kill_processes(self):
        """Kill MindBack processes"""
        try:
            # Kill by process name
            subprocess.run(['taskkill', '/f', '/im', 'uvicorn.exe'], capture_output=True)
            subprocess.run(['taskkill', '/f', '/im', 'node.exe'], capture_output=True)
            
            # Kill PowerShell running our script
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'powershell' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                        if 'run_setup.ps1' in cmdline:
                            proc.terminate()
                except:
                    pass
        except:
            pass
    
    def monitor(self):
        """Simple monitoring"""
        for i in range(24):  # 2 minutes max
            if not self.is_running:
                break
            time.sleep(5)
            
            # Check if frontend is running
            if self.is_port_in_use(5173):
                self.icon.title = "MindBack - Running"
                break
        else:
            if self.is_running:
                self.icon.title = "MindBack - Check Status"
    
    def is_port_in_use(self, port):
        """Check if port is in use"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
        except:
            pass
        return False
    
    def quit_app(self, icon, item):
        """Quit application"""
        if self.is_running:
            self.kill_processes()
        self.icon.stop()
    
    def run(self):
        """Run the tray application"""
        self.icon.run()

if __name__ == "__main__":
    app = SimpleMindBackTray()
    app.run()
