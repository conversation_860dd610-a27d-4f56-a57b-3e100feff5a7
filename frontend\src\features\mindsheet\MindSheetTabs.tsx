/**
 * MindSheetTabs Component
 *
 * Displays tabs for all mindsheets and handles tab selection.
 * Tabs are displayed in a row above the footer.
 *
 * This component follows the Excel model where tabs provide navigation
 * between different worksheets in a workbook.
 */

import React, { useEffect, useState } from 'react';
import { useMindBookStore } from '../../core/state/MindBookStore';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import { useChatForkStore } from '../../components/ChatFork/ChatForkStore';
import { getMindMapStore } from '../../core/state/MindMapStoreFactory';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import { EnhancedMindMapManager } from '../../components/MindMap/components/Manager/EnhancedMindMapManager';
import ChatForkManager from '../../components/ChatFork/ChatForkManager';
import './MindSheetTabs.css';

const MindSheetTabs: React.FC = () => {
  // All hooks must be called at the top level, before any conditional returns
  const { sheets, activeSheetId, setActiveSheet } = useMindBookStore();

  // State for managers - removed governance chat state as it's handled elsewhere
  const [showMindMapManager, setShowMindMapManager] = useState(false);
  const [showChatForkManager, setShowChatForkManager] = useState(false);

  // Get the current active sheet info for the managers
  const activeSheet = activeSheetId ? sheets.find(s => s.id === activeSheetId) : null;
  const isActiveMindMapSheet = activeSheet?.contentType === MindSheetContentType.MINDMAP;
  const isActiveChatForkSheet = activeSheet?.contentType === MindSheetContentType.CHATFORK;

  // ALWAYS get the store for the active sheet to avoid conditional hooks
  // This will return a store even if the sheet isn't a mindmap (we just won't use it)
  // Use a fallback ID to ensure we always call getMindMapStore consistently
  const storeSheetId = activeSheetId || 'fallback';
  const activeMindMapStore = getMindMapStore(storeSheetId);

  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  // Auto-close managers if user switches to incompatible sheet type
  useEffect(() => {
    if (showMindMapManager && !isActiveMindMapSheet) {
      console.log('MindSheetTabs: Auto-closing MindMap Manager - active sheet is not a mindmap');
      setShowMindMapManager(false);
    }
    if (showChatForkManager && !isActiveChatForkSheet) {
      console.log('MindSheetTabs: Auto-closing ChatFork Manager - active sheet is not a chatfork');
      setShowChatForkManager(false);
    }
  }, [showMindMapManager, showChatForkManager, isActiveMindMapSheet, isActiveChatForkSheet]);

  // Handle tab click
  const handleTabClick = (sheetId: string, contentType: MindSheetContentType) => {
    console.log(`MindSheetTabs: Switching to sheet: ${sheetId}, type: ${contentType}`);

    // If this sheet is already active, do nothing
    if (sheetId === activeSheetId) {
      console.log('MindSheetTabs: Sheet is already active, no action needed');
      return;
    }



    // Find the sheet to get its content
    const sheet = sheets.find(s => s.id === sheetId);
    if (!sheet) {
      console.warn('MindSheetTabs: Could not find sheet with ID:', sheetId);
      return;
    }
    // Register the sheet switch event after locating the sheet
    RegistrationManager.registerEvent(EventType.SHEET_SWITCHED, {
      sheetId: sheetId,
      name: sheet.title || `Sheet ${sheetId}`,
      type: contentType.toLowerCase(),
      fromSheetId: activeSheetId || 'none'
    });

    console.log('MindSheetTabs: Found sheet:', {
      id: sheet.id,
      title: sheet.title,
      contentType: sheet.contentType,
      hasContent: !!sheet.content
    });

    // Hide any ChatFork that might be visible
    useChatForkStore.getState().hideChatFork();

    // Handle different content types
    switch (contentType) {
      case MindSheetContentType.CHATFORK:
        if (sheet.content) {
          console.log('MindSheetTabs: Found ChatFork sheet, showing content');

          // Show the ChatFork with the sheet's content after setting the active sheet
          // This ensures the MindSheet component is active before showing the ChatFork
          setActiveSheet(sheetId);

          // Short delay to ensure the sheet is active before showing the ChatFork
          setTimeout(() => {
            const chatForkStore = useChatForkStore.getState();
            chatForkStore.showChatFork(sheet.content, sheetId);
          }, 50);
        } else {
          console.warn('MindSheetTabs: ChatFork sheet has no content');
          setActiveSheet(sheetId);
        }
        break;

      case MindSheetContentType.MINDMAP:
        // For mindmap sheets, just set the active sheet
        // The MindSheet component will handle the initialization and state restoration
        console.log('MindSheetTabs: Setting active mindmap sheet');
        setActiveSheet(sheetId);

        // If the MindMapManager is open, it will automatically switch to the new sheet's data
        if (showMindMapManager) {
          console.log('MindSheetTabs: MindMapManager will automatically switch to new sheet:', sheetId);
        }
        break;

      default:
        console.log(`MindSheetTabs: Switching to sheet type: ${contentType}`);
        setActiveSheet(sheetId);
        break;
    }
  };

  // If no sheets, don't render anything (MOVED AFTER ALL HOOKS)
  if (sheets.length === 0) return null;

  // Manager handlers for MindMap
  const handleOpenMindMapManager = () => {
    if (activeSheetId && isActiveMindMapSheet) {
      setShowMindMapManager(true);
      console.log('MindSheetTabs: Opening MindMapManager for active sheet:', activeSheetId);
    } else {
      console.log('MindSheetTabs: Cannot open MindMap manager - no active mindmap sheet. ActiveSheetId:', activeSheetId, 'IsMindMapSheet:', isActiveMindMapSheet);
    }
  };

  const handleCloseMindMapManager = () => {
    setShowMindMapManager(false);
    console.log('MindSheetTabs: Closing MindMapManager');
  };

  // Manager handlers for ChatFork
  const handleOpenChatForkManager = () => {
    if (activeSheetId && isActiveChatForkSheet) {
      setShowChatForkManager(true);
      console.log('MindSheetTabs: Opening ChatForkManager for active sheet:', activeSheetId);
    } else {
      console.log('MindSheetTabs: Cannot open ChatFork manager - no active chatfork sheet. ActiveSheetId:', activeSheetId, 'IsChatForkSheet:', isActiveChatForkSheet);
    }
  };

  const handleCloseChatForkManager = () => {
    setShowChatForkManager(false);
    console.log('MindSheetTabs: Closing ChatForkManager');
  };

  return (
    <>
      <div className="mindsheet-tabs">
        {/* Sheet tabs */}
        {sheets.map(sheet => (
          <div
            key={sheet.id}
            id={`tab-${sheet.id}`}
            className={`mindsheet-tab ${sheet.id === activeSheetId ? 'active' : ''} ${sheet.contentType.toLowerCase()}`}
            onClick={() => handleTabClick(sheet.id, sheet.contentType)}
            title={sheet.title}
          >
            {sheet.title}
          </div>
        ))}

        {/* Control buttons - conditionally shown based on active sheet type */}
        <div className="mindsheet-controls">
          {/* MindMap Manager - only show for mindmap sheets */}
          {isActiveMindMapSheet && (
            <button
              className="mindsheet-control-button"
              onClick={handleOpenMindMapManager}
              title="Open MindMap Manager"
            >
              MindMap Manager
            </button>
          )}

          {/* ChatFork Manager - only show for chatfork sheets */}
          {isActiveChatForkSheet && (
            <button
              className="mindsheet-control-button"
              onClick={handleOpenChatForkManager}
              title="Open ChatFork Manager"
            >
              ChatFork Manager
            </button>
          )}
        </div>
      </div>

      {/* MindMap Manager Dialog - Only for MindMap sheets */}
      {showMindMapManager && activeSheetId && isActiveMindMapSheet && (
        <EnhancedMindMapManager
          open={showMindMapManager}
          onClose={handleCloseMindMapManager}
          sheetId={activeSheetId}
          store={activeMindMapStore}
        />
      )}

      {/* ChatFork Manager Dialog - Only for ChatFork sheets */}
      {showChatForkManager && activeSheetId && isActiveChatForkSheet && (
        <ChatForkManager
          open={showChatForkManager}
          onClose={handleCloseChatForkManager}
          sheetId={activeSheetId}
        />
      )}
    </>
  );
};

export default MindSheetTabs;
