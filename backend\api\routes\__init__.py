"""
Routes package for API endpoints
"""

from fastapi import APIRouter

# Create a main router
api_router = APIRouter()

# Import and include routers
from .llm import router as llm_router
from .logging import router as logging_router
from .snapshot import router as snapshot_router

# Include routers in the main router
api_router.include_router(llm_router)
api_router.include_router(logging_router)
api_router.include_router(snapshot_router)